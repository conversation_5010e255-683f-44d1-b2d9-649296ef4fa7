import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MultiSelect, MultiSelectOption } from '@/components/ui/multi-select';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTopics } from '@/hooks/useTopics';
import { supabase } from '@/integrations/supabase/client';
import { Mail, Sparkles } from 'lucide-react';

interface SimpleEmailSubscriptionProps {
  className?: string;
}

export const SimpleEmailSubscription: React.FC<SimpleEmailSubscriptionProps> = ({ className = '' }) => {
  const { t } = useTranslation();
  const { language } = useLanguage();
  const { topics } = useTopics();
  const { toast } = useToast();

  const [email, setEmail] = useState('');
  const [selectedTopicIds, setSelectedTopicIds] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 过滤活跃的topics
  const activeTopics = topics.filter(topic => topic.is_active);

  // 构建MultiSelect选项
  const topicOptions: MultiSelectOption[] = activeTopics.map(topic => ({
    value: topic.id,
    label: topic.name
  }));

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || selectedTopicIds.length === 0) {
      toast({
        title: t('emailSubscription.error'),
        description: t('emailSubscription.fillAllFields'),
        variant: 'destructive',
      });
      return;
    }

    // 验证email格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      toast({
        title: t('emailSubscription.error'),
        description: t('emailSubscription.invalidEmail'),
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // 调用新的简化订阅API
      const { data, error } = await supabase.functions.invoke('simple-email-subscription', {
        body: {
          email: email.trim().toLowerCase(),
          topic_ids: selectedTopicIds,
          language: language === 'zh' ? 'zh' : 'en'
        }
      });

      if (error) {
        throw error;
      }

      if (data.success) {
        toast({
          title: t('emailSubscription.success'),
          description: t('emailSubscription.subscribeSuccess'),
        });

        // 重置表单
        setEmail('');
        setSelectedTopicIds([]);
      } else {
        throw new Error(data.error || 'Subscription failed');
      }
    } catch (error: any) {
      console.error('Subscription error:', error);
      toast({
        title: t('emailSubscription.error'),
        description: error.message || t('emailSubscription.subscribeError'),
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className={`w-full max-w-md mx-auto ${className}`}>
      <CardHeader className="text-center">
        <div className="w-12 h-12 mx-auto bg-gradient-primary rounded-full flex items-center justify-center mb-4">
          <Mail className="h-6 w-6 text-white" />
        </div>
        <CardTitle className="text-xl font-bold">
          {t('emailSubscription.subscribe')}
        </CardTitle>
        <CardDescription>
          {t('emailSubscription.simpleDescription')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Email Input */}
          <div className="space-y-2">
            <Label htmlFor="email">{t('emailSubscription.email')}</Label>
            <Input
              id="email"
              type="email"
              placeholder={t('emailSubscription.emailPlaceholder')}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              disabled={isSubmitting}
            />
          </div>

          {/* Topic Selection */}
          <div className="space-y-2">
            <Label htmlFor="topics">{t('emailSubscription.topics')}</Label>
            <MultiSelect
              options={topicOptions}
              selected={selectedTopicIds}
              onChange={setSelectedTopicIds}
              placeholder={t('emailSubscription.selectTopics')}
              searchPlaceholder={t('common.search')}
              emptyText={t('common.noResults')}
              selectAllText={t('common.selectAll')}
              clearAllText={t('common.clear')}
              showSelectAll={true}
              className="w-full"
            />
          </div>

          {/* Submit Button */}
          <Button 
            type="submit" 
            className="w-full bg-gradient-primary hover:shadow-glow-intense"
            disabled={isSubmitting}
          >
            {isSubmitting ? t('emailSubscription.subscribing') : t('emailSubscription.subscribeNow')}
          </Button>

          {/* Info Text */}
          <p className="text-xs text-muted-foreground text-center">
            {t('emailSubscription.simpleInfo')}
          </p>
        </form>
      </CardContent>
    </Card>
  );
};
