import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const htmlHeaders = {
  ...corsHeaders,
  'Content-Type': 'text/html; charset=utf-8',
  'Cache-Control': 'no-cache, no-store, must-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0'
}

interface UnsubscribeResult {
  success: boolean;
  message: string;
  unsubscribedCount?: number;
  subscriptions?: any[];
}

/**
 * 邮件取消订阅处理器
 * 支持全部取消订阅和按topic取消订阅
 * 支持注册用户和非注册用户
 */
Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const url = new URL(req.url);
    const token = url.searchParams.get('token');
    const topicId = url.searchParams.get('topic'); // 可选：指定要取消的topic
    const action = url.searchParams.get('action') || 'unsubscribe'; // unsubscribe 或 view

    if (!token) {
      return new Response(
        generateUnsubscribePage(false, 'Invalid unsubscribe link'),
        {
          headers: htmlHeaders,
          status: 400,
        }
      );
    }

    // 解码token (可能是user_id或email)
    let decodedToken: string;
    try {
      decodedToken = atob(token);
    } catch (error) {
      return new Response(
        generateUnsubscribePage(false, 'Invalid unsubscribe token'),
        {
          headers: htmlHeaders,
          status: 400,
        }
      );
    }

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // 检查是否是来自前端的请求（通过Accept header判断）
    const acceptHeader = req.headers.get('accept') || '';
    const isJsonRequest = acceptHeader.includes('application/json') || req.headers.get('content-type')?.includes('application/json');

    // 处理取消订阅请求
    if (action === 'unsubscribe') {
      const result = await handleUnsubscribe(supabaseClient, decodedToken, topicId);

      if (isJsonRequest) {
        // 返回JSON响应给前端
        return new Response(
          JSON.stringify(result),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: result.success ? 200 : 400,
          }
        );
      } else {
        // 返回HTML响应给邮件链接
        if (result.success) {
          return new Response(
            generateUnsubscribePage(true, undefined, result),
            {
              headers: htmlHeaders,
              status: 200,
            }
          );
        } else {
          return new Response(
            generateUnsubscribePage(false, result.message),
            {
              headers: htmlHeaders,
              status: 400,
            }
          );
        }
      }
    } else if (action === 'view') {
      // 显示订阅管理页面
      const subscriptions = await getUserSubscriptions(supabaseClient, decodedToken);

      if (isJsonRequest) {
        // 返回JSON响应给前端
        return new Response(
          JSON.stringify({ success: true, subscriptions }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 200,
          }
        );
      } else {
        // 返回HTML响应给邮件链接
        return new Response(
          generateSubscriptionManagePage(subscriptions, decodedToken),
          {
            headers: htmlHeaders,
            status: 200,
          }
        );
      }
    }

    return new Response(
      generateUnsubscribePage(false, 'Invalid action'),
      {
        headers: htmlHeaders,
        status: 400,
      }
    );

  } catch (error) {
    console.error('Email unsubscribe error:', error);
    return new Response(
      generateUnsubscribePage(false, 'An unexpected error occurred'),
      {
        headers: { ...corsHeaders, 'Content-Type': 'text/html' },
        status: 500,
      }
    );
  }
});

/**
 * 处理取消订阅逻辑
 */
async function handleUnsubscribe(
  supabaseClient: any,
  decodedToken: string,
  topicId?: string
): Promise<UnsubscribeResult> {
  try {
    // 判断token是user_id还是email
    const isEmail = decodedToken.includes('@');

    // 查询用户的订阅
    let subscriptionsQuery = supabaseClient
      .from('subscriptions')
      .select('id, email, user_id, topic_id, language, enabled')
      .eq('enabled', true);

    if (isEmail) {
      subscriptionsQuery = subscriptionsQuery.eq('email', decodedToken);
    } else {
      subscriptionsQuery = subscriptionsQuery.eq('user_id', decodedToken);
    }

    if (topicId) {
      subscriptionsQuery = subscriptionsQuery.eq('topic_id', topicId);
    }

    const { data: subscriptions, error: fetchError } = await subscriptionsQuery;

    if (fetchError) {
      console.error('Failed to fetch subscriptions:', fetchError);
      return { success: false, message: 'Failed to fetch subscriptions' };
    }

    if (!subscriptions || subscriptions.length === 0) {
      return {
        success: false,
        message: topicId ? 'No active subscription found for this topic' : 'No active subscriptions found'
      };
    }

    // 更新订阅状态为disabled
    const subscriptionIds = subscriptions.map(sub => sub.id);
    const { error: updateError } = await supabaseClient
      .from('subscriptions')
      .update({
        enabled: false,
        updated_at: new Date().toISOString()
      })
      .in('id', subscriptionIds);

    if (updateError) {
      console.error('Failed to update subscriptions:', updateError);
      return { success: false, message: 'Failed to update subscriptions' };
    }

    // 同步到listmonk (如果有email)
    if (subscriptions.length > 0) {
      await syncToListmonk(subscriptions[0].email, false);
    }

    console.log(`Successfully unsubscribed ${subscriptions.length} subscription(s) for token: ${decodedToken}`);

    return {
      success: true,
      message: topicId
        ? `Successfully unsubscribed from topic: ${subscriptions[0]?.topics?.name || topicId}`
        : `Successfully unsubscribed from ${subscriptions.length} topic(s)`,
      unsubscribedCount: subscriptions.length,
      subscriptions: subscriptions
    };

  } catch (error) {
    console.error('Error in handleUnsubscribe:', error);
    return { success: false, message: 'An unexpected error occurred' };
  }
}

/**
 * 获取用户的所有订阅
 */
async function getUserSubscriptions(supabaseClient: any, decodedToken: string) {
  try {
    const { data: subscriptions, error } = await supabaseClient
      .rpc('get_user_subscriptions_by_token', {
        p_token: decodedToken
      });

    if (error) {
      console.error('Failed to fetch user subscriptions:', error);
      return [];
    }

    return subscriptions || [];
  } catch (error) {
    console.error('Error in getUserSubscriptions:', error);
    return [];
  }
}

/**
 * 同步到listmonk
 */
async function syncToListmonk(email: string, subscribe: boolean) {
  try {
    const baseUrl = Deno.env.get('LISTMONK_BASE_URL') || 'https://listmonk4me.zeabur.app';
    const apiUser = Deno.env.get('LISTMONK_API_USER') || '';
    const apiToken = Deno.env.get('LISTMONK_API_TOKEN') || '';
    const authHeader = Deno.env.get('LISTMONK_API_AUTH');

    let headers: Record<string, string> = { 'Content-Type': 'application/json' };
    if (authHeader) {
      headers.Authorization = authHeader;
    } else if (apiUser && apiToken) {
      headers.Authorization = `token ${apiUser}:${apiToken}`;
    } else {
      console.warn('Listmonk credentials not configured, skipping sync');
      return;
    }

    // 查找subscriber
    const q = encodeURIComponent(`subscribers.email = '${email.replace(/'/g, "''")}'`);
    const searchRes = await fetch(`${baseUrl}/api/subscribers?page=1&per_page=1&query=${q}`, { headers });

    if (!searchRes.ok) {
      console.warn('Failed to search subscriber in listmonk:', searchRes.status);
      return;
    }

    const searchData = await searchRes.json();
    const subscriber = searchData?.data?.results?.[0];

    if (subscriber) {
      // 更新subscriber状态
      const status = subscribe ? 'enabled' : 'unsubscribed';
      const updateRes = await fetch(`${baseUrl}/api/subscribers/${subscriber.id}`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({ status })
      });

      if (!updateRes.ok) {
        console.warn('Failed to update subscriber status in listmonk:', updateRes.status);
      } else {
        console.log(`Updated listmonk subscriber ${email} status to ${status}`);
      }
    }
  } catch (error) {
    console.warn('Error syncing to listmonk:', error);
  }
}

/**
 * 生成取消订阅页面HTML
 */
function generateUnsubscribePage(success: boolean, errorMessage?: string, result?: UnsubscribeResult): string {
  const title = success ? 'Successfully Unsubscribed' : 'Unsubscribe Failed';

  let message = '';
  if (success && result) {
    message = result.message;
  } else if (success) {
    message = 'You have been successfully unsubscribed from FeedMe.Today daily emails.';
  } else {
    message = `Failed to unsubscribe: ${errorMessage}`;
  }

  const additionalContent = success
    ? `<p style="color: #6b7280; margin-bottom: 30px;">You can re-subscribe anytime by visiting our website and updating your preferences.</p>
       <a href="https://feedme.today/content-summary" style="display: inline-block; background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500;">Visit FeedMe.Today</a>`
    : `<p style="color: #6b7280; margin-bottom: 30px;">Please try again or contact support if the problem persists.</p>
       <a href="https://feedme.today" style="display: inline-block; background-color: #6b7280; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500;">Back to Website</a>`;

  return `<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title} - FeedMe.Today</title>
</head>
<body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f9fafb; min-height: 100vh; display: flex; align-items: center; justify-content: center;">

    <div style="max-width: 500px; width: 100%; background: white; border-radius: 12px; padding: 40px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">

        <!-- Icon -->
        <div style="font-size: 48px; margin-bottom: 20px;">
            ${success ? '✅' : '❌'}
        </div>

        <!-- Title -->
        <h1 style="color: #1f2937; margin: 0 0 20px 0; font-size: 24px; font-weight: 600;">
            ${title}
        </h1>

        <!-- Message -->
        <p style="color: ${success ? '#059669' : '#dc2626'}; margin-bottom: 20px; font-size: 16px;">
            ${message}
        </p>

        <!-- Additional Content -->
        ${additionalContent}

        <!-- Footer -->
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; font-size: 14px; margin: 0;">
                © 2024 FeedMe.Today - Smart Content Aggregation Platform
            </p>
        </div>

    </div>

</body>
</html>`;
}

/**
 * 生成订阅管理页面HTML
 */
function generateSubscriptionManagePage(subscriptions: any[], token: string): string {
  const activeSubscriptions = subscriptions.filter(sub => sub.enabled);
  const inactiveSubscriptions = subscriptions.filter(sub => !sub.enabled);

  const activeSubsHtml = activeSubscriptions.length > 0
    ? activeSubscriptions.map(sub => {
        const topicName = sub.topics?.name || `Topic ${sub.topic_id}`;
        const unsubscribeUrl = `?token=${btoa(token)}&action=unsubscribe&topic=${sub.topic_id}`;
        return `
          <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px; margin-bottom: 12px; background: #f9fafb;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <div>
                <h3 style="margin: 0 0 4px 0; font-size: 16px; color: #1f2937;">${topicName}</h3>
                <p style="margin: 0; font-size: 14px; color: #6b7280;">Language: ${sub.language.toUpperCase()}</p>
              </div>
              <a href="${unsubscribeUrl}" style="background-color: #dc2626; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-size: 14px;">Unsubscribe</a>
            </div>
          </div>
        `;
      }).join('')
    : '<p style="color: #6b7280; text-align: center; padding: 20px;">No active subscriptions found.</p>';

  const unsubscribeAllUrl = `?token=${btoa(token)}&action=unsubscribe`;

  return `<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Subscriptions - FeedMe.Today</title>
</head>
<body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f9fafb; min-height: 100vh;">

    <div style="max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; padding: 40px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">

        <!-- Header -->
        <div style="text-align: center; margin-bottom: 40px;">
            <h1 style="color: #1f2937; margin: 0 0 10px 0; font-size: 28px; font-weight: 600;">
                📧 Manage Your Subscriptions
            </h1>
            <p style="color: #6b7280; margin: 0; font-size: 16px;">
                Choose which topics you want to unsubscribe from
            </p>
        </div>

        <!-- Active Subscriptions -->
        <div style="margin-bottom: 30px;">
            <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 20px; font-weight: 600;">
                Active Subscriptions (${activeSubscriptions.length})
            </h2>
            ${activeSubsHtml}
        </div>

        <!-- Actions -->
        ${activeSubscriptions.length > 0 ? `
        <div style="text-align: center; margin-bottom: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <a href="${unsubscribeAllUrl}" style="display: inline-block; background-color: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; margin-right: 12px;">Unsubscribe from All</a>
            <a href="https://feedme.today/content-summary" style="display: inline-block; background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500;">Manage Preferences</a>
        </div>
        ` : `
        <div style="text-align: center; margin-bottom: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <a href="https://feedme.today/content-summary" style="display: inline-block; background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500;">Visit FeedMe.Today</a>
        </div>
        `}

        <!-- Footer -->
        <div style="text-align: center; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; font-size: 14px; margin: 0;">
                © 2024 FeedMe.Today - Smart Content Aggregation Platform
            </p>
        </div>

    </div>

</body>
</html>`;
}
