-- 创建处理unsubscribe相关的数据库函数

-- 1. 获取没有活跃订阅的邮箱地址
CREATE OR REPLACE FUNCTION get_emails_without_active_subscriptions()
RETURNS TABLE (email TEXT) AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT s.email
  FROM subscriptions s
  WHERE s.email NOT IN (
    SELECT DISTINCT s2.email 
    FROM subscriptions s2 
    WHERE s2.enabled = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. 批量取消订阅函数
CREATE OR REPLACE FUNCTION batch_unsubscribe_by_email(
  p_email TEXT,
  p_topic_ids UUID[] DEFAULT NULL
)
RETURNS TABLE (
  unsubscribed_count INTEGER,
  affected_subscriptions JSONB
) AS $$
DECLARE
  affected_subs JSONB;
  unsub_count INTEGER;
BEGIN
  -- 如果指定了topic_ids，只取消指定的topics
  IF p_topic_ids IS NOT NULL THEN
    UPDATE subscriptions 
    SET enabled = false, updated_at = NOW()
    WHERE email = p_email 
      AND topic_id = ANY(p_topic_ids)
      AND enabled = true;
  ELSE
    -- 否则取消所有订阅
    UPDATE subscriptions 
    SET enabled = false, updated_at = NOW()
    WHERE email = p_email 
      AND enabled = true;
  END IF;

  GET DIAGNOSTICS unsub_count = ROW_COUNT;

  -- 获取受影响的订阅信息
  SELECT jsonb_agg(
    jsonb_build_object(
      'id', s.id,
      'topic_id', s.topic_id,
      'topic_name', t.name,
      'language', s.language
    )
  ) INTO affected_subs
  FROM subscriptions s
  JOIN topics t ON s.topic_id = t.id
  WHERE s.email = p_email 
    AND s.enabled = false
    AND s.updated_at >= NOW() - INTERVAL '1 minute';

  RETURN QUERY SELECT unsub_count, COALESCE(affected_subs, '[]'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. 批量取消订阅函数（按user_id）
CREATE OR REPLACE FUNCTION batch_unsubscribe_by_user_id(
  p_user_id UUID,
  p_topic_ids UUID[] DEFAULT NULL
)
RETURNS TABLE (
  unsubscribed_count INTEGER,
  affected_subscriptions JSONB
) AS $$
DECLARE
  affected_subs JSONB;
  unsub_count INTEGER;
BEGIN
  -- 如果指定了topic_ids，只取消指定的topics
  IF p_topic_ids IS NOT NULL THEN
    UPDATE subscriptions 
    SET enabled = false, updated_at = NOW()
    WHERE user_id = p_user_id 
      AND topic_id = ANY(p_topic_ids)
      AND enabled = true;
  ELSE
    -- 否则取消所有订阅
    UPDATE subscriptions 
    SET enabled = false, updated_at = NOW()
    WHERE user_id = p_user_id 
      AND enabled = true;
  END IF;

  GET DIAGNOSTICS unsub_count = ROW_COUNT;

  -- 获取受影响的订阅信息
  SELECT jsonb_agg(
    jsonb_build_object(
      'id', s.id,
      'topic_id', s.topic_id,
      'topic_name', t.name,
      'language', s.language,
      'email', s.email
    )
  ) INTO affected_subs
  FROM subscriptions s
  JOIN topics t ON s.topic_id = t.id
  WHERE s.user_id = p_user_id 
    AND s.enabled = false
    AND s.updated_at >= NOW() - INTERVAL '1 minute';

  RETURN QUERY SELECT unsub_count, COALESCE(affected_subs, '[]'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. 获取用户订阅信息函数
CREATE OR REPLACE FUNCTION get_user_subscriptions_by_token(
  p_token TEXT
)
RETURNS TABLE (
  id UUID,
  email TEXT,
  user_id UUID,
  topic_id UUID,
  topic_name TEXT,
  topic_name_zh TEXT,
  topic_name_en TEXT,
  language TEXT,
  enabled BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
  is_email BOOLEAN;
BEGIN
  -- 判断token是email还是user_id
  is_email := p_token LIKE '%@%';
  
  IF is_email THEN
    RETURN QUERY
    SELECT 
      s.id,
      s.email,
      s.user_id,
      s.topic_id,
      t.name as topic_name,
      t.name_zh as topic_name_zh,
      t.name_en as topic_name_en,
      s.language,
      s.enabled,
      s.created_at
    FROM subscriptions s
    JOIN topics t ON s.topic_id = t.id
    WHERE s.email = p_token
    ORDER BY s.created_at DESC;
  ELSE
    RETURN QUERY
    SELECT 
      s.id,
      s.email,
      s.user_id,
      s.topic_id,
      t.name as topic_name,
      t.name_zh as topic_name_zh,
      t.name_en as topic_name_en,
      s.language,
      s.enabled,
      s.created_at
    FROM subscriptions s
    JOIN topics t ON s.topic_id = t.id
    WHERE s.user_id = p_token::UUID
    ORDER BY s.created_at DESC;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 授权给service role
GRANT EXECUTE ON FUNCTION get_emails_without_active_subscriptions() TO service_role;
GRANT EXECUTE ON FUNCTION batch_unsubscribe_by_email(TEXT, UUID[]) TO service_role;
GRANT EXECUTE ON FUNCTION batch_unsubscribe_by_user_id(UUID, UUID[]) TO service_role;
GRANT EXECUTE ON FUNCTION get_user_subscriptions_by_token(TEXT) TO service_role;

-- 创建索引优化查询性能（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_subscriptions_email_enabled') THEN
        CREATE INDEX idx_subscriptions_email_enabled ON subscriptions(email, enabled);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_subscriptions_user_id_enabled') THEN
        CREATE INDEX idx_subscriptions_user_id_enabled ON subscriptions(user_id, enabled);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_subscriptions_updated_at') THEN
        CREATE INDEX idx_subscriptions_updated_at ON subscriptions(updated_at);
    END IF;
END $$;
