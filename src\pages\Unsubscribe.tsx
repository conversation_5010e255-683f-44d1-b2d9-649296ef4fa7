import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { CheckCircle, XCircle, Loader2, Mail, Settings, Eye } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

interface Subscription {
  id: string;
  email: string;
  user_id: string | null;
  topic_id: string;
  topic_name: string;
  topic_name_zh: string;
  topic_name_en: string;
  language: string;
  enabled: boolean;
  created_at: string;
}

interface UnsubscribeState {
  status: 'loading' | 'success' | 'error' | 'view';
  message: string;
  subscriptions?: Subscription[];
  unsubscribedCount?: number;
}

const Unsubscribe: React.FC = () => {
  const [searchParams] = useSearchParams();
  const { language } = useLanguage();
  const [state, setState] = useState<UnsubscribeState>({
    status: 'loading',
    message: ''
  });

  const token = searchParams.get('token');
  const topicId = searchParams.get('topic');
  const action = searchParams.get('action') || 'unsubscribe';
  const isZh = language === 'zh';

  useEffect(() => {
    const handleRequest = async () => {
      if (!token) {
        setState({
          status: 'error',
          message: isZh ? '无效的取消订阅链接' : 'Invalid unsubscribe link'
        });
        return;
      }

      try {
        const url = new URL(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/email-unsubscribe`);
        url.searchParams.set('token', token);
        if (topicId) url.searchParams.set('topic', topicId);
        url.searchParams.set('action', action);

        const response = await fetch(url.toString(), {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          if (action === 'view') {
            // This will be handled by the edge function returning HTML
            // We need to parse the response differently for view action
            const text = await response.text();
            if (text.includes('Manage Your Subscriptions')) {
              // The edge function returned HTML, redirect to it
              window.location.href = url.toString();
              return;
            }
          } else {
            // Parse JSON response for unsubscribe action
            const data = await response.json();
            setState({
              status: 'success',
              message: data.message || (isZh
                ? '您已成功取消订阅 FeedMe.Today 每日邮件。'
                : 'You have been successfully unsubscribed from FeedMe.Today daily emails.'),
              unsubscribedCount: data.unsubscribedCount
            });
          }
        } else {
          const errorText = await response.text();
          setState({
            status: 'error',
            message: isZh
              ? '取消订阅失败，请稍后重试。'
              : 'Failed to unsubscribe. Please try again later.'
          });
        }
      } catch (error) {
        console.error('Unsubscribe error:', error);
        setState({
          status: 'error',
          message: isZh
            ? '发生意外错误，请稍后重试。'
            : 'An unexpected error occurred. Please try again later.'
        });
      }
    };

    handleRequest();
  }, [token, topicId, action, isZh]);

  const handleUnsubscribeTopic = async (targetTopicId: string) => {
    try {
      const url = new URL(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/email-unsubscribe`);
      url.searchParams.set('token', token!);
      url.searchParams.set('topic', targetTopicId);
      url.searchParams.set('action', 'unsubscribe');

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setState(prev => ({
          ...prev,
          status: 'success',
          message: data.message || (isZh ? '成功取消订阅该主题' : 'Successfully unsubscribed from topic'),
          subscriptions: prev.subscriptions?.filter(sub => sub.topic_id !== targetTopicId)
        }));
      }
    } catch (error) {
      console.error('Error unsubscribing from topic:', error);
    }
  };

  const handleUnsubscribeAll = async () => {
    try {
      const url = new URL(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/email-unsubscribe`);
      url.searchParams.set('token', token!);
      url.searchParams.set('action', 'unsubscribe');

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setState({
          status: 'success',
          message: data.message || (isZh ? '成功取消所有订阅' : 'Successfully unsubscribed from all topics'),
          unsubscribedCount: data.unsubscribedCount
        });
      }
    } catch (error) {
      console.error('Error unsubscribing from all:', error);
    }
  };

  const getIcon = () => {
    switch (state.status) {
      case 'loading':
        return <Loader2 className="w-16 h-16 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="w-16 h-16 text-green-500" />;
      case 'error':
        return <XCircle className="w-16 h-16 text-red-500" />;
      case 'view':
        return <Mail className="w-16 h-16 text-blue-500" />;
    }
  };

  const getTitle = () => {
    switch (state.status) {
      case 'loading':
        return isZh ? '正在处理...' : 'Processing...';
      case 'success':
        return isZh ? '取消订阅成功' : 'Successfully Unsubscribed';
      case 'error':
        return isZh ? '取消订阅失败' : 'Unsubscribe Failed';
      case 'view':
        return isZh ? '管理您的订阅' : 'Manage Your Subscriptions';
    }
  };

  // If we're in view mode and have subscriptions, show the management interface
  if (state.status === 'view' && state.subscriptions) {
    const activeSubscriptions = state.subscriptions.filter(sub => sub.enabled);

    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-6 text-center">
            <div className="flex justify-center mb-4">
              <Mail className="w-12 h-12 text-blue-500" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {isZh ? '📧 管理您的订阅' : '📧 Manage Your Subscriptions'}
            </h1>
            <p className="text-gray-600">
              {isZh ? '选择您想要取消订阅的主题' : 'Choose which topics you want to unsubscribe from'}
            </p>
          </div>

          {/* Active Subscriptions */}
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              {isZh ? `活跃订阅 (${activeSubscriptions.length})` : `Active Subscriptions (${activeSubscriptions.length})`}
            </h2>

            {activeSubscriptions.length > 0 ? (
              <div className="space-y-4">
                {activeSubscriptions.map((subscription) => {
                  const topicName = isZh ? subscription.topic_name_zh || subscription.topic_name : subscription.topic_name_en || subscription.topic_name;
                  return (
                    <div key={subscription.id} className="border border-gray-200 rounded-lg p-4 flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900">{topicName}</h3>
                        <p className="text-sm text-gray-500">
                          {isZh ? '语言' : 'Language'}: {subscription.language.toUpperCase()}
                        </p>
                      </div>
                      <button
                        onClick={() => handleUnsubscribeTopic(subscription.topic_id)}
                        className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                      >
                        {isZh ? '取消订阅' : 'Unsubscribe'}
                      </button>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">
                  {isZh ? '没有找到活跃的订阅' : 'No active subscriptions found'}
                </p>
              </div>
            )}

            {/* Actions */}
            {activeSubscriptions.length > 0 && (
              <div className="mt-8 pt-6 border-t border-gray-200 flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={handleUnsubscribeAll}
                  className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors font-medium"
                >
                  {isZh ? '取消所有订阅' : 'Unsubscribe from All'}
                </button>
                <a
                  href="/"
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium text-center"
                >
                  {isZh ? '访问网站' : 'Visit Website'}
                </a>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="text-center mt-8">
            <p className="text-sm text-gray-500">
              © 2024 FeedMe.Today
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Default view for loading, success, and error states
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        {/* Icon */}
        <div className="flex justify-center mb-6">
          {getIcon()}
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          {getTitle()}
        </h1>

        {/* Message */}
        <p className={`text-base mb-6 ${
          state.status === 'success' ? 'text-green-600' :
          state.status === 'error' ? 'text-red-600' :
          'text-gray-600'
        }`}>
          {state.message}
        </p>

        {/* Success Details */}
        {state.status === 'success' && state.unsubscribedCount && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <p className="text-sm text-green-700">
              {isZh
                ? `已取消 ${state.unsubscribedCount} 个订阅`
                : `Unsubscribed from ${state.unsubscribedCount} subscription(s)`}
            </p>
          </div>
        )}

        {/* Action Buttons */}
        {state.status === 'success' && (
          <div className="space-y-3">
            <p className="text-sm text-gray-500 mb-4">
              {isZh
                ? '您可以随时访问我们的网站重新订阅或管理偏好设置。'
                : 'You can re-subscribe anytime by visiting our website and updating your preferences.'}
            </p>
            <div className="space-y-2">
              <a
                href="/"
                className="inline-block w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                {isZh ? '访问 FeedMe.Today' : 'Visit FeedMe.Today'}
              </a>
              {token && (
                <a
                  href={`/unsubscribe?token=${token}&action=view`}
                  className="inline-block w-full bg-gray-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-700 transition-colors"
                >
                  {isZh ? '查看剩余订阅' : 'View Remaining Subscriptions'}
                </a>
              )}
            </div>
          </div>
        )}

        {state.status === 'error' && (
          <div className="space-y-3">
            <p className="text-sm text-gray-500 mb-4">
              {isZh
                ? '如果问题持续存在，请联系客服或稍后重试。'
                : 'Please try again or contact support if the problem persists.'}
            </p>
            <a
              href="/"
              className="inline-block w-full bg-gray-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-700 transition-colors"
            >
              {isZh ? '返回网站' : 'Back to Website'}
            </a>
          </div>
        )}

        {/* Footer */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            © 2024 FeedMe.Today
          </p>
        </div>
      </div>
    </div>
  );
};

export default Unsubscribe;
