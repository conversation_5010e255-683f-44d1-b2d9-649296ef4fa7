import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface SimpleSubscriptionRequest {
  email: string;
  topic_ids: string[];
  language: 'zh' | 'en';
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client with service role key for full access
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ success: false, error: 'Method not allowed' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 405,
        }
      )
    }

    const requestData: SimpleSubscriptionRequest = await req.json()
    
    // Validate required fields
    if (!requestData.email || !requestData.topic_ids || !Array.isArray(requestData.topic_ids) || requestData.topic_ids.length === 0 || !requestData.language) {
      return new Response(
        JSON.stringify({ success: false, error: 'Missing required fields: email, topic_ids (non-empty array), language' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(requestData.email)) {
      return new Response(
        JSON.stringify({ success: false, error: 'Invalid email format' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Validate language
    if (!['zh', 'en'].includes(requestData.language)) {
      return new Response(
        JSON.stringify({ success: false, error: 'Invalid language. Must be zh or en' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Validate all topics exist and are active
    const { data: topics, error: topicsError } = await supabaseClient
      .from('topics')
      .select('id, name, is_active')
      .in('id', requestData.topic_ids)
      .eq('is_active', true)

    if (topicsError) {
      console.error('Topics validation error:', topicsError)
      return new Response(
        JSON.stringify({ success: false, error: 'Failed to validate topics' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }

    if (!topics || topics.length !== requestData.topic_ids.length) {
      const validTopicIds = topics?.map(t => t.id) || []
      const invalidTopicIds = requestData.topic_ids.filter(id => !validTopicIds.includes(id))
      console.error('Invalid or inactive topics:', invalidTopicIds)
      return new Response(
        JSON.stringify({ success: false, error: `Invalid or inactive topics: ${invalidTopicIds.join(', ')}` }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Check if user is authenticated (optional)
    let userId: string | null = null
    const authHeader = req.headers.get('Authorization')
    if (authHeader) {
      try {
        const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
          authHeader.replace('Bearer ', '')
        )
        if (!authError && user) {
          userId = user.id
        }
      } catch (error) {
        // Ignore auth errors for non-registered users
        console.log('Auth check failed (non-registered user):', error)
      }
    }

    // First, delete all existing subscriptions for this email to ensure clean state
    const { error: deleteError } = await supabaseClient
      .from('subscriptions')
      .delete()
      .eq('email', requestData.email.trim().toLowerCase())

    if (deleteError) {
      console.error('Failed to delete existing subscriptions:', deleteError)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to clean existing subscriptions: ' + deleteError.message
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }

    // Create subscriptions for all selected topics
    const subscriptionResults = []
    const createdTopics = []

    for (const topicId of requestData.topic_ids) {
      const { data: subscriptionId, error: subscriptionError } = await supabaseClient
        .rpc('upsert_subscription', {
          p_email: requestData.email.trim().toLowerCase(),
          p_topic_id: topicId,
          p_language: requestData.language,
          p_user_id: userId,
          p_enabled: true
        })

      if (subscriptionError) {
        console.error(`Subscription creation error for topic ${topicId}:`, subscriptionError)
        return new Response(
          JSON.stringify({
            success: false,
            error: `Failed to create subscription for topic ${topicId}: ` + subscriptionError.message
          }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
          }
        )
      }

      subscriptionResults.push(subscriptionId)
      const topic = topics.find(t => t.id === topicId)
      if (topic) {
        createdTopics.push(topic.name)
      }
    }

    // Log successful subscriptions
    console.log(`Simple subscriptions created: ${requestData.email} -> ${createdTopics.join(', ')} (${requestData.language})`)

    return new Response(
      JSON.stringify({
        success: true,
        subscription_ids: subscriptionResults,
        subscriptions_count: subscriptionResults.length,
        message: `${subscriptionResults.length} subscription(s) created successfully`,
        topic_names: createdTopics,
        language: requestData.language
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Simple email subscription error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Internal server error: ' + (error as Error).message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
